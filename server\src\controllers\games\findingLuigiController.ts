/**
 * Finding Luigi Game Controller
 * Server-side game logic for Finding Luigi hidden object game
 */

import type { Server, Socket } from 'socket.io';
import { GameService } from '../../services/gameService';
import { logger } from '../../utils/logger';
import { GAME_TYPES } from '../../utils/constants';
import type {
  GameInitResult,
  GameStartData,
  GameEndData,
  GameActionData,
  EndReason
} from '../../types/game';
import {FINDING_LUIGI_CONSTANTS, FACE_NAMES } from '../../types/findingLuigi';
import type {
  FindingLuigiGameState,
  ServerGameLayout,
  ServerFaceData,
  ClientGameLayout,
  FindingLuigiRoundData,
  FaceSelectActionData,
  FaceSelectResult,
  FindingLuigiGameStartedData,
  FindingLuigiActionResultData,
  // FINDING_LUIGI_CONSTANTS,
  // FACE_NAMES,
  FaceName
} from '../../types/findingLuigi';
import { submitGameScore, ScoreSubmissionData } from '../../utils/externalApi';
import { getAuthenticatedUser } from '../../middleware/auth';
import { sessionService } from '../../services/sessionService';

export default class FindingLuigiController {
  private gameService: GameService;
  private gameStates: Map<string, FindingLuigiGameState> = new Map();
  private roundTimers: Map<string, NodeJS.Timeout> = new Map(); // Keyed by roomId:userId
  private socketMap: Map<string, Socket> = new Map(); // Keyed by roomId:userId

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Generate a unique face ID
   */
  private generateFaceId(): string {
    return 'face_' + Math.random().toString(36) + '_' + Date.now().toString(36);
  }

  /**
   * Convert server layout data to client-safe layout data (removes target information)
   */
  private toClientLayout(serverLayout: ServerGameLayout): ClientGameLayout {
    return {
      faces: serverLayout.faces.map(face => ({
        id: face.id,
        faceType: face.faceType,
        position: face.position,
        zIndex: face.zIndex
      })),
      backgroundKey: serverLayout.backgroundKey,
      layoutSeed: serverLayout.layoutSeed
    };
  }

  /**
   * Initialize a new Finding Luigi game session (called at client load)
   */
  initializeGame(roomId: string, socket: Socket, userId?: string): GameInitResult {
    // Check if room already has a game state
    let gameState = this.gameService.getGameState(roomId, userId);

    if (gameState) {
      // Game state exists, check if we can initialize
      if (gameState.status === 'active') {
        return { success: false, message: 'Game is already active' };
      }
      // If game ended, clean up and create new state to allow restart
      if (gameState.status === 'ended') {
        this.cleanupGame(roomId, userId);
        gameState = this.gameService.createGameState(
          roomId,
          'finding-luigi',
          FINDING_LUIGI_CONSTANTS.LIVES.INITIAL_LIVES,
          userId
        );
      }
    } else {
      // Create new game state
      gameState = this.gameService.createGameState(
        roomId,
        'finding-luigi',
        FINDING_LUIGI_CONSTANTS.LIVES.INITIAL_LIVES,
        userId
      );
    }

    // Initialize Finding Luigi specific game state
    const findingLuigiState: FindingLuigiGameState = {
      currentRound: null,
      roundsCompleted: 0,
      isRoundActive: false,
      roundStartTime: null,
      targetsFound: [],
      targetsRemaining: 1, // Always one blue hat face to find
      currentLayout: null
    };

    const key = `${roomId}:${userId ?? ''}`;
    this.gameStates.set(key, findingLuigiState);

    // Store socket reference for timer updates
    this.socketMap.set(key, socket);

    // Generate the first round layout and targets
    const firstRound = this.generateNewRound(roomId, 1, userId);
    if (!firstRound) {
      return { success: false, message: 'Failed to generate first round' };
    }

    // Store the first round but don't start it
    const findingLuigiStateUpdated = this.gameStates.get(key);
    if (findingLuigiStateUpdated) {
      findingLuigiStateUpdated.currentRound = firstRound;
    }

    logger.info(`Finding Luigi game initialized for room ${roomId}`);
    return { success: true, gameState };
  }

  /**
   * Start the game (called after countdown ends)
   */
  startGame(roomId: string, socket: Socket, userId?: string): GameInitResult {
    const gameState = this.gameService.getGameState(roomId, userId);
    const key = `${roomId}:${userId ?? ''}`;
    const findingLuigiState = this.gameStates.get(key);

    if (!gameState) {
      return { success: false, message: 'Game not initialized. Call initializeGame first.' };
    }

    if (!findingLuigiState) {
      return { success: false, message: 'Game data not found. Call initializeGame first.' };
    }

    if (gameState.status === 'active') {
      return { success: false, message: 'Game is already active' };
    }

    // If game ended, clean up and allow restart
    if (gameState.status === 'ended') {
      this.cleanupGame(roomId, userId);
      return { success: false, message: 'Game ended, please reinitialize' };
    }

    // Start the game using GameService
    const started = this.gameService.startGame(roomId, socket, userId);
    if (!started) {
      return { success: false, message: 'Failed to start game' };
    }

    // Start the first round
    if (findingLuigiState.currentRound) {
      this.startRound(roomId, findingLuigiState.currentRound, userId);
    }

    logger.info(`Finding Luigi game started for room ${roomId}`);
    return { success: true, gameState };
  }

  /**
   * Generate a new round with face layout and targets
   */
  private generateNewRound(roomId: string, roundNumber: number, userId?: string): FindingLuigiRoundData | null {
    try {
      // Calculate face count for this round (progressive difficulty)
      const faceCount = Math.min(
        FINDING_LUIGI_CONSTANTS.STARTING_FACE_COUNT +
        (roundNumber - 1) * FINDING_LUIGI_CONSTANTS.FACE_COUNT_INCREASE,
        FINDING_LUIGI_CONSTANTS.MAX_FACE_COUNT
      );

      // Generate the game layout with calculated face count
      const layout = this.generateGameLayout(roomId, userId, faceCount);
      if (!layout) {
        return null;
      }

      // Target is always the blue hat face (only one on screen)
      const targetFaces = [FINDING_LUIGI_CONSTANTS.TARGET_FACE];

      const roundData: FindingLuigiRoundData = {
        roundNumber,
        layout: this.toClientLayout(layout),
        targets: targetFaces,
        timeLimit: FINDING_LUIGI_CONSTANTS.ROUND_TIME * 1000,
        startTime: Date.now(),
        targetsRemaining: targetFaces.length
      };

      // Store the server layout for validation
      const key = `${roomId}:${userId ?? ''}`;
      const findingLuigiState = this.gameStates.get(key);
      if (findingLuigiState) {
        findingLuigiState.currentLayout = layout;
      }

      return roundData;
    } catch (error) {
      logger.error(`Error generating round for room ${roomId}:`, error);
      return null;
    }
  }

  /**
   * Generate a game layout with faces scattered across the screen
   */
  private generateGameLayout(roomId: string, userId?: string, faceCount?: number): ServerGameLayout | null {
    try {
      const faces: ServerFaceData[] = [];
      const positions: { x: number; y: number }[] = [];

      // Use provided face count or default
      const totalFaces = faceCount || FINDING_LUIGI_CONSTANTS.STARTING_FACE_COUNT;

      // Generate random positions for faces
      for (let i = 0; i < totalFaces; i++) {
        let position: { x: number; y: number };
        let attempts = 0;
        const maxAttempts = 100;

        // Find a position that doesn't overlap with existing faces
        do {
          position = {
            x: sessionService.getPrngInt(roomId, 540 - 2 * FINDING_LUIGI_CONSTANTS.LAYOUT.PADDING) + FINDING_LUIGI_CONSTANTS.LAYOUT.PADDING,
            y: sessionService.getPrngInt(roomId, 960 - 2 * FINDING_LUIGI_CONSTANTS.LAYOUT.PADDING) + FINDING_LUIGI_CONSTANTS.LAYOUT.PADDING
          };
          attempts++;
        } while (
          attempts < maxAttempts &&
          positions.some(pos => 
            Math.sqrt(Math.pow(pos.x - position.x, 2) + Math.pow(pos.y - position.y, 2)) < FINDING_LUIGI_CONSTANTS.LAYOUT.MIN_DISTANCE
          )
        );

        if (attempts >= maxAttempts) {
          logger.warn(`Could not find non-overlapping position for face ${i} in room ${roomId}`);
          continue;
        }

        positions.push(position);

        // Determine face type - first face is always blue hat (target), rest are random
        let faceType: FaceName;
        let isTarget = false;

        if (i === 0) {
          // First face is always the blue hat target
          faceType = FINDING_LUIGI_CONSTANTS.TARGET_FACE;
          isTarget = true;
        } else {
          // Other faces are random (excluding blue hat)
          const otherFaces = FACE_NAMES.filter(name => name !== FINDING_LUIGI_CONSTANTS.TARGET_FACE);
          faceType = otherFaces[sessionService.getPrngInt(roomId, otherFaces.length)];
        }
        
        // Random scale and rotation
        const scale = FINDING_LUIGI_CONSTANTS.LAYOUT.MIN_SCALE + 
          (sessionService.getPrngInt(roomId, 100) / 100) * 
          (FINDING_LUIGI_CONSTANTS.LAYOUT.MAX_SCALE - FINDING_LUIGI_CONSTANTS.LAYOUT.MIN_SCALE);
        
        const rotation = 0; // No rotation for simplicity

        const face: ServerFaceData = {
          id: this.generateFaceId(),
          faceType,
          position: {
            x: position.x,
            y: position.y,
            scale,
            rotation
          },
          zIndex: i,
          isTarget,
          targetForPlayers: isTarget ? [userId || 'default'] : []
        };

        faces.push(face);
      }

      // Shuffle faces to randomize target positions
      for (let i = faces.length - 1; i > 0; i--) {
        const j = sessionService.getPrngInt(roomId, i + 1);
        [faces[i], faces[j]] = [faces[j], faces[i]];
      }

      const layout: ServerGameLayout = {
        faces,
        backgroundKey: 'forest_background',
        layoutSeed: roomId + '_' + Date.now(),
        playerTargets: new Map([[userId || 'default', faces.filter(f => f.isTarget).map(f => f.id)]])
      };

      return layout;
    } catch (error) {
      logger.error(`Error generating game layout for room ${roomId}:`, error);
      return null;
    }
  }

  /**
   * Start a round with timer
   */
  private startRound(roomId: string, roundData: FindingLuigiRoundData, userId?: string): void {
    const key = `${roomId}:${userId ?? ''}`;
    const findingLuigiState = this.gameStates.get(key);
    
    if (!findingLuigiState) {
      logger.error(`No game state found for room ${roomId}`);
      return;
    }

    findingLuigiState.isRoundActive = true;
    findingLuigiState.roundStartTime = Date.now();

    // Set round timer
    const timer = setTimeout(() => {
      this.handleRoundTimeout(roomId, userId);
    }, roundData.timeLimit);

    this.roundTimers.set(key, timer);

    logger.info(`Round ${roundData.roundNumber} started for room ${roomId}`);
  }

  /**
   * Handle round timeout
   */
  private handleRoundTimeout(roomId: string, userId?: string): void {
    const key = `${roomId}:${userId ?? ''}`;
    const gameState = this.gameService.getGameState(roomId, userId);
    const findingLuigiState = this.gameStates.get(key);
    const socket = this.socketMap.get(key);

    if (!gameState || !findingLuigiState || !socket) {
      return;
    }

    findingLuigiState.isRoundActive = false;

    // End the game due to timeout
    this.endGame(roomId, 'timeout', userId);
  }

  /**
   * Clean up game resources
   */
  private cleanupGame(roomId: string, userId?: string): void {
    const key = `${roomId}:${userId ?? ''}`;
    
    // Clear timers
    const timer = this.roundTimers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.roundTimers.delete(key);
    }

    // Clear game state
    this.gameStates.delete(key);
    this.socketMap.delete(key);

    logger.info(`Cleaned up Finding Luigi game for room ${roomId}`);
  }

  /**
   * Handle face selection
   */
  handleFaceSelection(roomId: string, faceId: string, reactionTime: number = 0, userId?: string): FaceSelectResult {
    const key = `${roomId}:${userId ?? ''}`;
    const gameState = this.gameService.getGameState(roomId, userId);
    const findingLuigiState = this.gameStates.get(key);

    // Check if game and state are valid
    if (!gameState || !findingLuigiState || gameState.status !== 'active') {
      logger.info(`Face selection ignored for room ${roomId}: game not active or state missing`);
      return {
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState?.score || 0,
        newLives: gameState?.lives || 0,
        gameEnded: true,
        faceId,
        wasTarget: false,
        targetsRemaining: 0,
        roundComplete: false
      };
    }

    if (!findingLuigiState.isRoundActive || !findingLuigiState.currentLayout) {
      logger.info(`Face selection ignored for room ${roomId}: round not active`);
      return {
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        faceId,
        wasTarget: false,
        targetsRemaining: findingLuigiState.targetsRemaining,
        roundComplete: false
      };
    }

    // Find the selected face
    const selectedFace = findingLuigiState.currentLayout.faces.find(face => face.id === faceId);
    if (!selectedFace) {
      logger.warn(`Face ${faceId} not found in layout for room ${roomId}`);
      return {
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        faceId,
        wasTarget: false,
        targetsRemaining: findingLuigiState.targetsRemaining,
        roundComplete: false
      };
    }

    const wasTarget = selectedFace.isTarget && selectedFace.targetForPlayers.includes(userId || 'default');
    let points = 0;
    let newScore = gameState.score;
    let newLives = gameState.lives;
    let gameEnded = false;
    let roundComplete = false;

    if (wasTarget && !findingLuigiState.targetsFound.includes(faceId)) {
      // Correct target found
      points = FINDING_LUIGI_CONSTANTS.SCORING.TARGET_FOUND;

      // Add time bonus for quick finds
      if (reactionTime > 0 && reactionTime < FINDING_LUIGI_CONSTANTS.SCORING.TIME_BONUS_THRESHOLD) {
        const timeBonus = Math.min(
          Math.floor((FINDING_LUIGI_CONSTANTS.SCORING.TIME_BONUS_THRESHOLD - reactionTime) / 100),
          FINDING_LUIGI_CONSTANTS.SCORING.TIME_BONUS_MAX
        );
        points += timeBonus;
      }

      newScore += points;
      findingLuigiState.targetsFound.push(faceId);
      findingLuigiState.targetsRemaining--;

      // Check if round is complete (blue hat found)
      if (findingLuigiState.targetsRemaining <= 0) {
        roundComplete = true;
        findingLuigiState.roundsCompleted++;
        findingLuigiState.isRoundActive = false;

        // Clear round timer
        const timer = this.roundTimers.get(key);
        if (timer) {
          clearTimeout(timer);
          this.roundTimers.delete(key);
        }
      }
    } else {
      // Wrong face selected or already found
      points = -FINDING_LUIGI_CONSTANTS.SCORING.WRONG_FACE_PENALTY;
      newScore = Math.max(0, newScore + points);
      newLives--;

      if (newLives <= 0) {
        gameEnded = true;
      }
    }

    // Update game state
    gameState.score = newScore;
    gameState.lives = newLives;

    if (gameEnded) {
      gameState.status = 'ended';
    }

    logger.info(`Face selection result for room ${roomId}: target=${wasTarget}, points=${points}, score=${newScore}, lives=${newLives}`);

    const result: FaceSelectResult = {
      success: true,
      isCorrect: wasTarget,
      points,
      newScore,
      newLives,
      gameEnded,
      faceId,
      wasTarget,
      targetFaceType: wasTarget ? selectedFace.faceType : undefined,
      targetsRemaining: findingLuigiState.targetsRemaining,
      roundComplete
    };

    // Generate next round if current round is complete and game not ended
    if (roundComplete && !gameEnded) {
      const nextRound = this.generateNewRound(roomId, findingLuigiState.roundsCompleted + 1, userId);
      if (nextRound) {
        findingLuigiState.currentRound = nextRound;
        result.nextRound = nextRound;
        // Start the next round
        this.startRound(roomId, nextRound, userId);
      }
    }

    return result;
  }

  /**
   * Setup socket event handlers for Finding Luigi
   */
  public setupSocketHandlers(socket: Socket): void {
    // Get authenticated user to check if this is a Finding Luigi game
    const user = getAuthenticatedUser(socket);
    if (!user || user.gameId !== 'finding-luigi') {
      return; // Only handle events for Finding Luigi games
    }

    // Game initialization event (called at client load)
    socket.on('init', (data) => {
      if (data.gameId === 'finding-luigi') {
        this.handleGameInit(socket, data);
      }
    });

    // Game start event (called after countdown)
    socket.on('start', (data) => {
      if (data.gameId === 'finding-luigi') {
        this.handleGameStart(socket, data);
      }
    });

    // Generic game end event
    socket.on('end', (data) => {
      if (data.gameId === 'finding-luigi') {
        this.handleGameEnd(socket, data);
      }
    });

    // Game action event (face selection)
    socket.on('action', (data) => {
      if (data.gameId === 'finding-luigi') {
        this.handleGameAction(socket, data);
      }
    });
  }

  /**
   * Handle game initialization
   */
  private handleGameInit(socket: Socket, data: GameStartData): void {
    const { roomId, gameId } = data;
    const user = getAuthenticatedUser(socket);
    const userId = user?.userId;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for game initialization'
      });
      return;
    }

    try {
      const result = this.initializeGame(roomId, socket, userId);

      if (result.success) {
        const key = `${roomId}:${userId ?? ''}`;
        const findingLuigiState = this.gameStates.get(key);

        if (findingLuigiState?.currentRound) {
          const gameStartedData: FindingLuigiGameStartedData = {
            gameState: {
              score: result.gameState?.score || 0,
              lives: result.gameState?.lives || 3,
              isActive: result.gameState?.status === 'active',
              startTime: result.gameState?.startTime
            },
            firstRound: findingLuigiState.currentRound,
            message: 'Finding Luigi game initialized successfully'
          };

          socket.emit('initialized', gameStartedData);
        }
      } else {
        socket.emit('error', {
          message: result.message || 'Failed to initialize game'
        });
      }
    } catch (error) {
      logger.error(`Error initializing Finding Luigi game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game start
   */
  private handleGameStart(socket: Socket, data: GameStartData): void {
    const { roomId, gameId } = data;
    const user = getAuthenticatedUser(socket);
    const userId = user?.userId;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for game start'
      });
      return;
    }

    try {
      const result = this.startGame(roomId, socket, userId);

      if (result.success) {
        socket.emit('started', {
          gameState: {
            score: result.gameState?.score || 0,
            lives: result.gameState?.lives || 3,
            isActive: result.gameState?.status === 'active',
            startTime: result.gameState?.startTime
          },
          message: 'Finding Luigi game started successfully'
        });
      } else {
        socket.emit('error', {
          message: result.message || 'Failed to start game'
        });
      }
    } catch (error) {
      logger.error(`Error starting Finding Luigi game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game end
   */
  private handleGameEnd(socket: Socket, data: GameEndData): void {
    const { roomId, gameId, reason } = data;
    const user = getAuthenticatedUser(socket);
    const userId = user?.userId;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for game end'
      });
      return;
    }

    try {
      this.endGame(roomId, reason || 'manual', userId);

      socket.emit('ended', {
        message: 'Finding Luigi game ended successfully',
        reason: reason || 'manual'
      });
    } catch (error) {
      logger.error(`Error ending Finding Luigi game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game action
   */
  private handleGameAction(socket: Socket, data: GameActionData): void {
    const { roomId, gameId, action } = data;

    if (!roomId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing required data for game action'
      });
      return;
    }

    try {
      // Handle specific action types for Finding Luigi
      switch (action.type) {
        case 'face_select':
          this.handleFaceSelectAction(socket, data as FaceSelectActionData);
          break;
        default:
          socket.emit('error', {
            message: `Unknown action type: ${action.type}`
          });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle face select action
   */
  private handleFaceSelectAction(socket: Socket, data: FaceSelectActionData): void {
    const { roomId, action } = data;
    const { faceId, reactionTime } = action.data;

    if (!faceId) {
      socket.emit('error', {
        message: 'Missing face ID'
      });
      return;
    }

    try {
      // Process the face selection
      const user = getAuthenticatedUser(socket);
      const userId = user?.userId;
      const result = this.handleFaceSelection(roomId, faceId, reactionTime || 0, userId);

      if (result.success) {
        const actionResultData: FindingLuigiActionResultData = {
          actionType: 'face_select',
          data: {
            faceId,
            wasTarget: result.wasTarget,
            targetFaceType: result.targetFaceType,
            isCorrect: result.isCorrect,
            points: result.points,
            newScore: result.newScore,
            newLives: result.newLives,
            gameEnded: result.gameEnded,
            targetsRemaining: result.targetsRemaining,
            nextRound: result.nextRound,
            roundComplete: result.roundComplete
          }
        };

        socket.emit('action_result', actionResultData);

        // Handle game end if needed
        if (result.gameEnded) {
          this.endGame(roomId, 'no_lives', userId);
        }
      } else {
        socket.emit('error', {
          message: 'Failed to process face selection'
        });
      }
    } catch (error) {
      logger.error(`Error processing face selection in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Cleanup method called when a user disconnects
   */
  public cleanup(roomId: string, userId: string): void {
    this.cleanupGame(roomId, userId);
  }

  /**
   * End the game
   */
  private endGame(roomId: string, reason: EndReason, userId?: string): void {
    const gameState = this.gameService.getGameState(roomId, userId);
    if (!gameState) return;

    // End the game using GameService
    this.gameService.endGame(roomId, reason, userId);

    // Clean up resources
    this.cleanupGame(roomId, userId);

    logger.info(`Finding Luigi game ended for room ${roomId}, reason: ${reason}`);
  }
}
