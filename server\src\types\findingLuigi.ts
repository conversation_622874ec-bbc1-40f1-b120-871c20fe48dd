/**
 * Finding Luigi Game Types and Interfaces
 * Server-side types for the Finding Luigi hidden object game
 */

import type { BaseActionResult, GameActionData } from './game';

// Face types available in the game
export type FaceName = 'normal_face' | 'blue_hat_face' | 'frenna_face' | 'red_flower_face';

// Position data for face sprites
export interface FacePosition {
  x: number;
  y: number;
  scale: number; // Random scale between 0.8-1.2 for variety
  rotation: number; // Random rotation for natural look
}

// Face data structure sent to client (secure - no target info)
export interface ClientFaceData {
  id: string; // Unique random ID for this face
  faceType: FaceName;
  position: FacePosition;
  zIndex: number; // For layering
}

// Server-only face data structure (includes target info)
export interface ServerFaceData extends ClientFaceData {
  isTarget: boolean; // Whether this face is a target for any player
  targetForPlayers: string[]; // Array of player IDs who need to find this face
}

// Game layout data sent to client
export interface ClientGameLayout {
  faces: ClientFaceData[];
  backgroundKey: string;
  layoutSeed: string; // For reproducible layouts
}

// Server-only game layout (includes target info)
export interface ServerGameLayout extends ClientGameLayout {
  faces: ServerFaceData[];
  playerTargets: Map<string, string[]>; // playerId -> array of target face IDs
}

// Round data for Finding Luigi
export interface FindingLuigiRoundData {
  roundNumber: number;
  layout: ClientGameLayout;
  targets: FaceName[]; // Target faces for this player
  timeLimit: number; // in milliseconds
  startTime: number;
  targetsRemaining: number;
}

// Game state specific to Finding Luigi
export interface FindingLuigiGameState {
  currentRound: FindingLuigiRoundData | null;
  roundsCompleted: number;
  isRoundActive: boolean;
  roundStartTime: number | null;
  targetsFound: string[]; // Array of found target face IDs
  targetsRemaining: number;
  currentLayout: ServerGameLayout | null;
}

// Action types for Finding Luigi
export type FindingLuigiActionType = 'face_select';

// Face selection action data
export interface FaceSelectActionData extends GameActionData {
  action: {
    type: 'face_select';
    data: {
      faceId: string; // ID of the selected face
      reactionTime?: number; // time taken to find and select the face
      clickTime: number; // timestamp when face was clicked
      clickPosition: { x: number; y: number }; // where on screen the click occurred
    };
  };
}

// Action result for face selection
export interface FaceSelectResult extends BaseActionResult {
  faceId: string;
  wasTarget: boolean;
  targetFaceType?: FaceName;
  targetsRemaining: number;
  nextRound?: FindingLuigiRoundData;
  roundComplete: boolean;
}

// Socket event data structures specific to Finding Luigi
export interface FindingLuigiStartData {
  roomId: string;
  gameId: string;
}

export interface FindingLuigiGameStartedData {
  gameState: {
    score: number;
    lives: number;
    isActive: boolean;
    startTime?: number;
  };
  firstRound: FindingLuigiRoundData;
  message: string;
}

export interface FindingLuigiActionResultData {
  actionType: 'face_select';
  data: {
    faceId: string;
    wasTarget: boolean;
    targetFaceType?: FaceName;
    isCorrect: boolean;
    points: number;
    newScore: number;
    newLives: number;
    gameEnded: boolean;
    targetsRemaining: number;
    nextRound?: FindingLuigiRoundData;
    roundComplete: boolean;
  };
}

// Constants for Finding Luigi
export const FINDING_LUIGI_CONSTANTS = {
  ROUND_TIME: 30, // 30 seconds per round

  // Progressive difficulty - start with fewer faces, add more each round
  STARTING_FACE_COUNT: 5,
  FACE_COUNT_INCREASE: 3, // Add 3 more faces each round
  MAX_FACE_COUNT: 20,

  // Only one blue hat face (target) per round
  TARGET_FACE: 'frenna_face' as FaceName,

  LAYOUT: {
    MIN_SCALE: 0.8,
    MAX_SCALE: 1.2,
    PADDING: 60, // Minimum distance from screen edges
    MIN_DISTANCE: 100, // Minimum distance between face centers
  },

  // Movement settings
  MOVEMENT: {
    SPEED_MIN: 20, // pixels per second
    SPEED_MAX: 60,
    DIRECTION_CHANGE_INTERVAL: 2000, // Change direction every 2 seconds
  },

  SCORING: {
    TARGET_FOUND: 100, // Points for finding the blue hat face
    WRONG_FACE_PENALTY: 20, // Points deducted for clicking wrong face
    TIME_BONUS_MAX: 50, // Max bonus for quick finds
    TIME_BONUS_THRESHOLD: 5000, // Under 5 seconds for bonus
  },

  LIVES: {
    INITIAL_LIVES: 3,
    DEDUCT_ON_WRONG: 1,
  },

  MAX_ROUNDS: 15
} as const;

// Face definitions (matching client-side assets)
export const FACE_NAMES: readonly FaceName[] = [
  'normal_face', 
  'blue_hat_face', 
  'frenna_face', 
  'red_flower_face'
] as const;

// Background options for variety
export const BACKGROUND_KEYS = [
  'forest_background',
  'city_background', 
  'park_background'
] as const;
