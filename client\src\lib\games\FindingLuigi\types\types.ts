/**
 * Finding Luigi Game Types
 * Client-side types for the Finding Luigi hidden object game
 */

import type { SocketClient } from '$lib/socket';

// Face types available in the game
export type FaceName = 'normal_face' | 'blue_hat_face' | 'frenna_face' | 'red_flower_face';

// Position data for face sprites
export interface FacePosition {
  x: number;
  y: number;
  scale: number;
  rotation: number;
}

// Face data structure received from server
export interface FaceData {
  id: string;
  faceType: FaceName;
  position: FacePosition;
  zIndex: number;
}

// Game layout data received from server
export interface GameLayout {
  faces: FaceData[];
  backgroundKey: string;
  layoutSeed: string;
}

// Round data for Finding Luigi
export interface RoundData {
  roundNumber: number;
  layout: GameLayout;
  targets: FaceName[];
  timeLimit: number;
  startTime: number;
  targetsRemaining: number;
}

// Game configuration interface
export interface FindingLuigiConfig {
  gameId: string;
  containerId: string;
  socketClient?: SocketClient;
  roomId?: string;
  onScoreUpdate?: (score: number) => void;
  onGameComplete?: (finalScore: number) => void;
}

// Face sprite interface for Phaser objects
export interface FaceSprite extends Phaser.GameObjects.Image {
  faceData: FaceData;
  isTarget: boolean;
  isFound: boolean;
  originalScale: number;
  originalRotation: number;
}

// Target display interface
export interface TargetDisplay {
  faceType: FaceName;
  sprite: Phaser.GameObjects.Image;
  frame: Phaser.GameObjects.Image;
  checkmark?: Phaser.GameObjects.Image;
  found: boolean;
}

// Score popup interface
export interface ScorePopup {
  text: Phaser.GameObjects.Text;
  tween: Phaser.Tweens.Tween;
}

// Game state interface
export interface FindingLuigiGameState {
  currentRound: RoundData | null;
  targets: FaceName[];
  targetsFound: string[];
  targetsRemaining: number;
  score: number;
  lives: number;
  timeRemaining: number;
  isPlaying: boolean;
  isPaused: boolean;
}

// Socket event data structures
export interface FaceSelectData {
  faceId: string;
  reactionTime?: number;
  clickTime: number;
  clickPosition: { x: number; y: number };
}

export interface ActionResultData {
  actionType: 'face_select';
  data: {
    faceId: string;
    wasTarget: boolean;
    targetFaceType?: FaceName;
    isCorrect: boolean;
    points: number;
    newScore: number;
    newLives: number;
    gameEnded: boolean;
    targetsRemaining: number;
    nextRound?: RoundData;
    roundComplete: boolean;
  };
}

export interface GameStartedData {
  gameState: {
    score: number;
    lives: number;
    isActive: boolean;
    startTime?: number;
  };
  firstRound: RoundData;
  message: string;
}

// Animation configuration
export interface AnimationConfig {
  duration: number;
  ease: string;
  scale?: number;
  alpha?: number;
  rotation?: number;
  x?: number;
  y?: number;
}

// Click effect configuration
export interface ClickEffectConfig {
  x: number;
  y: number;
  color: number;
  scale: number;
  duration: number;
}

// Particle effect configuration
export interface ParticleConfig {
  x: number;
  y: number;
  texture: string;
  count: number;
  speed: { min: number; max: number };
  scale: { start: number; end: number };
  alpha: { start: number; end: number };
  lifespan: number;
}

// Game statistics
export interface GameStats {
  totalTargetsFound: number;
  totalClicks: number;
  accuracy: number;
  averageReactionTime: number;
  roundsCompleted: number;
  bestRoundTime: number;
}

// Leaderboard entry
export interface LeaderboardEntry {
  playerId: string;
  playerName: string;
  score: number;
  targetsFound: number;
  accuracy: number;
  completionTime: number;
}

// Game events
export type GameEvent = 
  | 'face_clicked'
  | 'target_found'
  | 'wrong_selection'
  | 'round_complete'
  | 'game_complete'
  | 'time_warning'
  | 'game_over';

// Event data interface
export interface GameEventData {
  type: GameEvent;
  data: any;
  timestamp: number;
}
