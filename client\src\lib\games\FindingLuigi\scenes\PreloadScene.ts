import * as Phaser from 'phaser';
import { gameActions } from '$lib/stores';
import { FACE_ASSETS, BACKGROUND_ASSETS, UI_ASSETS, AUDIO_ASSETS } from '../config/GameConfig';

export default class PreloadScene extends Phaser.Scene {
  constructor() {
    super('PreloadScene');
  }

  preload(): void {
    // Update progress bar as assets are loaded
    this.load.on('progress', (value: number) => {
      gameActions.updateLoadingProgress(value);
    });

    // Remove progress bar when complete
    this.load.on('complete', () => {
      gameActions.preloadComplete();
    });

    // Load face assets (SVG files)
    Object.values(FACE_ASSETS).forEach(asset => {
      this.load.svg(asset.key, asset.path);
    });

    // Load background assets
    Object.values(BACKGROUND_ASSETS).forEach(asset => {
      this.load.image(asset.key, asset.path);
    });

    // Load UI assets
    Object.values(UI_ASSETS).forEach(asset => {
      this.load.svg(asset.key, asset.path);
    });

    // Load audio assets (optional - may not exist yet)
    Object.values(AUDIO_ASSETS).forEach(asset => {
      this.load.audio(asset.key, asset.path);
    });

    // Load additional UI elements
    this.load.image('particle', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    
    // Create simple colored rectangles for UI elements
    this.load.image('button_bg', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==');
  }

  create(): void {
    // Assets are loaded, ready to proceed
    console.log('Finding Luigi assets loaded successfully');
    
    // Store asset references in registry for easy access
    this.game.registry.set('faceAssets', FACE_ASSETS);
    this.game.registry.set('backgroundAssets', BACKGROUND_ASSETS);
    this.game.registry.set('uiAssets', UI_ASSETS);
    this.game.registry.set('audioAssets', AUDIO_ASSETS);
  }
}
