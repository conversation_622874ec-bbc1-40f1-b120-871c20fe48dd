/**
 * Finding Luigi Game Configuration
 * Client-side configuration for the Finding Luigi hidden object game
 */

// Game dimensions and layout
export const GAME_CONFIG = {
  // Game area dimensions
  GAME_WIDTH: 540,
  GAME_HEIGHT: 960,
  
  // Layout configuration
  FACES_PER_LAYOUT: 25,
  TARGETS_PER_ROUND: 3,
  
  // Timing
  ROUND_TIME: 30, // seconds
  GAME_DURATION: 60, // seconds
  
  // Visual settings
  FACE_SCALE: {
    MIN: 0.6,
    MAX: 1.4,
  },
  
  FACE_ROTATION: {
    MIN: -15,
    MAX: 15,
  },
  
  // Layout constraints
  PADDING: 50, // Minimum distance from screen edges
  MIN_DISTANCE: 80, // Minimum distance between face centers
  
  // Animation settings
  FACE_HOVER_SCALE: 1.1,
  FACE_CLICK_SCALE: 0.9,
  ANIMATION_DURATION: 150,
  
  // UI settings
  TARGET_DISPLAY_SIZE: 80,
  TARGET_DISPLAY_PADDING: 10,
  
  // Scoring display
  SCORE_POPUP_DURATION: 1000,
  SCORE_POPUP_OFFSET: -50,
} as const;

// Face asset configuration
export const FACE_ASSETS = {
  normal_face: {
    key: 'normal_face',
    path: '/assets-find-someone/faces/normal_face.svg',
    displayName: 'Normal Face'
  },
  blue_hat_face: {
    key: 'blue_hat_face', 
    path: '/assets-find-someone/faces/blue_hat_face.svg',
    displayName: 'Blue Hat Face'
  },
  frenna_face: {
    key: 'frenna_face',
    path: '/assets-find-someone/faces/frenna_face.svg', 
    displayName: 'Frenna Face'
  },
  red_flower_face: {
    key: 'red_flower_face',
    path: '/assets-find-someone/faces/red_flower_face.svg',
    displayName: 'Red Flower Face'
  }
} as const;

// Background asset configuration
export const BACKGROUND_ASSETS = {
  forest_background: {
    key: 'forest_background',
    path: '/assets-find-someone/backgrounds/forest.svg',
    displayName: 'Forest'
  },
  city_background: {
    key: 'city_background', 
    path: '/assets-find-someone/backgrounds/city.svg',
    displayName: 'City'
  },
  park_background: {
    key: 'park_background',
    path: '/assets-find-someone/backgrounds/park.svg',
    displayName: 'Park'
  }
} as const;

// UI asset configuration
export const UI_ASSETS = {
  target_frame: {
    key: 'target_frame',
    path: '/assets-find-someone/ui/target_frame.svg'
  },
  found_checkmark: {
    key: 'found_checkmark', 
    path: '/assets-find-someone/ui/checkmark.svg'
  },
  timer_bg: {
    key: 'timer_bg',
    path: '/assets-find-someone/ui/timer_bg.png'
  }
} as const;

// Audio asset configuration  
export const AUDIO_ASSETS = {
  target_found: {
    key: 'target_found',
    path: '/assets-find-someone/audio/target_found.mp3'
  },
  wrong_selection: {
    key: 'wrong_selection',
    path: '/assets-find-someone/audio/wrong_selection.mp3'
  },
  round_complete: {
    key: 'round_complete',
    path: '/assets-find-someone/audio/round_complete.mp3'
  },
  game_music: {
    key: 'game_music',
    path: '/assets-find-someone/audio/background_music.mp3'
  }
} as const;

// Color scheme
export const COLORS = {
  PRIMARY: 0x4CAF50,
  SECONDARY: 0x2196F3, 
  SUCCESS: 0x8BC34A,
  WARNING: 0xFF9800,
  ERROR: 0xF44336,
  BACKGROUND: 0x1E1E1E,
  TEXT: 0xFFFFFF,
  TARGET_HIGHLIGHT: 0xFFD700,
  FOUND_HIGHLIGHT: 0x00FF00,
} as const;

// Game states
export enum FindingLuigiGameState {
  LOADING = 'loading',
  WAITING = 'waiting', 
  COUNTDOWN = 'countdown',
  PLAYING = 'playing',
  ROUND_COMPLETE = 'round_complete',
  GAME_COMPLETE = 'game_complete',
  PAUSED = 'paused'
}

// Export face names type for type safety
export type FaceName = keyof typeof FACE_ASSETS;
