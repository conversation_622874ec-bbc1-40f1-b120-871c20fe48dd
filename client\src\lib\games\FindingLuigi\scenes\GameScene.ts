import * as Phaser from 'phaser';
import type { SocketClient } from '$lib/socket';
import type {
  FindingLuigiConfig,
  RoundData,
  FaceData,
  FaceSprite,
  TargetDisplay,
  ActionResultData,
  GameStartedData
} from '../types/types';
import { GAME_CONFIG, COLORS } from '../config/GameConfig';

export default class GameScene extends Phaser.Scene {
  private socketClient: SocketClient | null = null;
  
  // Game state
  private currentRound: RoundData | null = null;
  private faceSprites: FaceSprite[] = [];
  private targetDisplays: TargetDisplay[] = [];
  private isGameActive: boolean = false;
  private isWaitingForServer: boolean = false;
  
  // UI elements
  private background!: Phaser.GameObjects.Image;
  private scoreText!: Phaser.GameObjects.Text;
  private livesText!: Phaser.GameObjects.Text;
  private timeText!: Phaser.GameObjects.Text;
  private targetsContainer!: Phaser.GameObjects.Container;
  private roundText!: Phaser.GameObjects.Text;
  
  // Timers
  private roundTimer: Phaser.Time.TimerEvent | null = null;
  
  constructor() {
    super('GameScene');
  }

  init(): void {
    // Reset game state
    this.isGameActive = false;
    this.isWaitingForServer = false;
    this.currentRound = null;
    this.faceSprites = [];
    this.targetDisplays = [];

    // Get socket client from game registry
    const gameConfig = this.registry.get('gameConfig') as FindingLuigiConfig;
    this.socketClient = gameConfig?.socketClient || null;

    // Setup socket event listeners
    this.setupSocketEventListeners();
  }

  /**
   * Setup socket event listeners for server communication
   */
  private setupSocketEventListeners(): void {
    if (!this.socketClient) return;

    // Add custom event listeners for game-specific handling
    this.socketClient.addCustomEventListener('initialized', (data: GameStartedData) => {
      console.log('Finding Luigi game initialized by server:', data);

      // Sync game state from server
      if (data.firstRound) {
        console.log('First round data received from server:', data.firstRound);
        this.currentRound = data.firstRound;
        this.setupRoundFromServer(data.firstRound);
      }

      // Start countdown after initialization
      this.startCountdown();
    });

    this.socketClient.addCustomEventListener('started', (data: any) => {
      console.log('Finding Luigi game started by server:', data);
      this.startGame();
    });

    this.socketClient.addCustomEventListener('action_result', (data: ActionResultData) => {
      console.log('Action result from server:', data);
      if (data.actionType === 'face_select') {
        this.handleFaceSelectResult(data.data);
      }
    });

    this.socketClient.addCustomEventListener('ended', (data: any) => {
      console.log('Finding Luigi game ended by server:', data);
      this.endGame();
    });

    this.socketClient.addCustomEventListener('error', (data: any) => {
      console.error('Server error:', data);
      this.showError(data.message || 'Unknown server error');
    });
  }

  create(): void {
    // Create UI elements
    this.createUI();

    // Initialize game first (client load)
    this.time.delayedCall(100, () => {
      this.initializeGame();
    });
  }

  private createUI(): void {
    const { width, height } = this.cameras.main;

    // Background (will be replaced when round data arrives)
    this.background = this.add.image(width / 2, height / 2, 'forest_background')
      .setDisplaySize(width, height);

    // Score display
    this.scoreText = this.add.text(20, 20, 'Score: 0', {
      fontSize: '24px',
      color: '#FFFFFF',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    });

    // Lives display
    this.livesText = this.add.text(20, 60, 'Lives: 3', {
      fontSize: '24px',
      color: '#FFFFFF',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    });

    // Time display
    this.timeText = this.add.text(width - 20, 20, 'Time: 30', {
      fontSize: '24px',
      color: '#FFFFFF',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(1, 0);

    // Round display
    this.roundText = this.add.text(width / 2, 20, 'Round 1', {
      fontSize: '28px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5, 0);

    // Targets container (will show target faces to find)
    this.targetsContainer = this.add.container(width / 2, 120);
  }

  private initializeGame(): void {
    if (!this.socketClient) {
      console.error('No socket client available');
      this.showError('Connection error');
      return;
    }

    console.log('Initializing Finding Luigi game...');
    this.socketClient.initGame();
  }

  private startCountdown(): void {
    console.log('Starting countdown...');
    
    const { width, height } = this.cameras.main;
    
    // Create countdown overlay
    const overlay = this.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.7);
    const countdownText = this.add.text(width / 2, height / 2, '3', {
      fontSize: '120px',
      color: '#FFFFFF',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    let count = 3;
    this.time.addEvent({
      delay: 1000,
      repeat: 2,
      callback: () => {
        count--;
        if (count > 0) {
          countdownText.setText(count.toString());
        } else {
          countdownText.setText('GO!');
          this.time.delayedCall(500, () => {
            overlay.destroy();
            countdownText.destroy();
            this.startGameFromCountdown();
          });
        }
      }
    });
  }

  private startGameFromCountdown(): void {
    if (!this.socketClient) return;

    console.log('Starting game from countdown...');
    this.socketClient.startGame();
  }

  private startGame(): void {
    console.log('Game started!');
    this.isGameActive = true;
    
    // Start round timer if we have round data
    if (this.currentRound) {
      this.startRoundTimer();
    }
  }

  private setupRoundFromServer(roundData: RoundData): void {
    console.log('Setting up round from server:', roundData);
    
    // Clear existing faces
    this.clearFaces();
    
    // Update round display
    this.roundText.setText(`Round ${roundData.roundNumber}`);
    
    // Create face sprites from layout
    this.createFaceSprites(roundData.layout.faces);
    
    // Create target displays
    this.createTargetDisplays(roundData.targets);
    
    // Update background if specified
    if (roundData.layout.backgroundKey) {
      this.background.setTexture(roundData.layout.backgroundKey);
    }
  }

  private createFaceSprites(faces: FaceData[]): void {
    faces.forEach(faceData => {
      // Create face sprite
      const sprite = this.add.image(
        faceData.position.x,
        faceData.position.y,
        faceData.faceType
      ) as FaceSprite;

      // Apply transformations
      sprite.setScale(faceData.position.scale);
      sprite.setRotation(faceData.position.rotation * Math.PI / 180);
      sprite.setDepth(faceData.zIndex);

      // Store face data
      sprite.faceData = faceData;
      sprite.isTarget = false; // Will be set when we know targets
      sprite.isFound = false;
      sprite.originalScale = faceData.position.scale;
      sprite.originalRotation = faceData.position.rotation * Math.PI / 180;

      // Make interactive
      sprite.setInteractive({ useHandCursor: true });
      
      sprite.on('pointerdown', () => {
        this.handleFaceClick(sprite);
      });

      sprite.on('pointerover', () => {
        if (!sprite.isFound) {
          sprite.setScale(sprite.originalScale * GAME_CONFIG.FACE_HOVER_SCALE);
        }
      });

      sprite.on('pointerout', () => {
        if (!sprite.isFound) {
          sprite.setScale(sprite.originalScale);
        }
      });

      this.faceSprites.push(sprite);
    });
  }

  private createTargetDisplays(targets: string[]): void {
    // Clear existing target displays
    this.targetsContainer.removeAll(true);
    this.targetDisplays = [];

    const startX = -(targets.length * (GAME_CONFIG.TARGET_DISPLAY_SIZE + GAME_CONFIG.TARGET_DISPLAY_PADDING)) / 2;

    targets.forEach((targetFace, index) => {
      const x = startX + index * (GAME_CONFIG.TARGET_DISPLAY_SIZE + GAME_CONFIG.TARGET_DISPLAY_PADDING);
      
      // Create target frame
      const frame = this.add.image(x, 0, 'target_frame')
        .setDisplaySize(GAME_CONFIG.TARGET_DISPLAY_SIZE, GAME_CONFIG.TARGET_DISPLAY_SIZE);
      
      // Create target face sprite
      const faceSprite = this.add.image(x, 0, targetFace)
        .setDisplaySize(GAME_CONFIG.TARGET_DISPLAY_SIZE * 0.8, GAME_CONFIG.TARGET_DISPLAY_SIZE * 0.8);

      this.targetsContainer.add([frame, faceSprite]);

      const targetDisplay: TargetDisplay = {
        faceType: targetFace as any,
        sprite: faceSprite,
        frame: frame,
        found: false
      };

      this.targetDisplays.push(targetDisplay);
    });

    // Mark face sprites that are targets
    this.faceSprites.forEach(sprite => {
      sprite.isTarget = targets.includes(sprite.faceData.faceType);
    });
  }

  private handleFaceClick(sprite: FaceSprite): void {
    if (!this.isGameActive || this.isWaitingForServer || sprite.isFound) {
      return;
    }

    console.log('Face clicked:', sprite.faceData.faceType, sprite.faceData.id);

    // Visual feedback
    sprite.setScale(sprite.originalScale * GAME_CONFIG.FACE_CLICK_SCALE);
    this.time.delayedCall(GAME_CONFIG.ANIMATION_DURATION, () => {
      if (!sprite.isFound) {
        sprite.setScale(sprite.originalScale);
      }
    });

    // Send selection to server
    this.sendFaceSelection(sprite);
  }

  private sendFaceSelection(sprite: FaceSprite): void {
    if (!this.socketClient) return;

    this.isWaitingForServer = true;

    const reactionTime = this.currentRound ? Date.now() - this.currentRound.startTime : 0;

    this.socketClient.sendFaceSelect(
      sprite.faceData.id,
      reactionTime,
      { x: sprite.x, y: sprite.y }
    );
  }

  private handleFaceSelectResult(data: any): void {
    this.isWaitingForServer = false;

    console.log('Face select result:', data);

    // Find the clicked face sprite
    const sprite = this.faceSprites.find(s => s.faceData.id === data.faceId);
    
    if (sprite) {
      if (data.wasTarget && data.isCorrect) {
        // Correct target found
        this.markFaceAsFound(sprite);
        this.markTargetAsFound(data.targetFaceType);
        this.showScorePopup(sprite.x, sprite.y, `+${data.points}`, COLORS.SUCCESS);
      } else {
        // Wrong face clicked
        this.showScorePopup(sprite.x, sprite.y, `${data.points}`, COLORS.ERROR);
      }
    }

    // Update UI
    this.updateScore(data.newScore);
    this.updateLives(data.newLives);

    // Handle round completion or game end
    if (data.gameEnded) {
      this.endGame();
    } else if (data.roundComplete && data.nextRound) {
      this.handleRoundComplete(data.nextRound);
    }
  }

  private markFaceAsFound(sprite: FaceSprite): void {
    sprite.isFound = true;
    sprite.setTint(COLORS.FOUND_HIGHLIGHT);
    sprite.setScale(sprite.originalScale * 1.2);
    
    // Add checkmark
    const checkmark = this.add.image(sprite.x, sprite.y, 'found_checkmark')
      .setDisplaySize(40, 40)
      .setDepth(sprite.depth + 1);
    
    // Animate checkmark
    checkmark.setScale(0);
    this.tweens.add({
      targets: checkmark,
      scale: 1,
      duration: 300,
      ease: 'Back.easeOut'
    });
  }

  private markTargetAsFound(targetFaceType: string): void {
    const targetDisplay = this.targetDisplays.find(t => t.faceType === targetFaceType);
    if (targetDisplay) {
      targetDisplay.found = true;
      
      // Add checkmark to target display
      const checkmark = this.add.image(0, 0, 'found_checkmark')
        .setDisplaySize(30, 30);
      
      targetDisplay.checkmark = checkmark;
      this.targetsContainer.add(checkmark);
      
      // Position checkmark over the target
      const targetIndex = this.targetDisplays.indexOf(targetDisplay);
      const startX = -(this.targetDisplays.length * (GAME_CONFIG.TARGET_DISPLAY_SIZE + GAME_CONFIG.TARGET_DISPLAY_PADDING)) / 2;
      checkmark.x = startX + targetIndex * (GAME_CONFIG.TARGET_DISPLAY_SIZE + GAME_CONFIG.TARGET_DISPLAY_PADDING);
      checkmark.y = 0;
    }
  }

  private showScorePopup(x: number, y: number, text: string, color: number): void {
    const popup = this.add.text(x, y, text, {
      fontSize: '32px',
      color: `#${color.toString(16).padStart(6, '0')}`,
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    this.tweens.add({
      targets: popup,
      y: y + GAME_CONFIG.SCORE_POPUP_OFFSET,
      alpha: 0,
      duration: GAME_CONFIG.SCORE_POPUP_DURATION,
      ease: 'Power2',
      onComplete: () => {
        popup.destroy();
      }
    });
  }

  private updateScore(score: number): void {
    this.scoreText.setText(`Score: ${score}`);
    this.registry.set('currentScore', score);
  }

  private updateLives(lives: number): void {
    this.livesText.setText(`Lives: ${lives}`);
    
    // Flash red if lives decreased
    if (lives < 3) {
      this.livesText.setColor('#FF0000');
      this.time.delayedCall(500, () => {
        this.livesText.setColor('#FFFFFF');
      });
    }
  }

  private startRoundTimer(): void {
    if (!this.currentRound) return;

    const duration = this.currentRound.timeLimit;
    let timeRemaining = Math.floor(duration / 1000);

    this.timeText.setText(`Time: ${timeRemaining}`);

    this.roundTimer = this.time.addEvent({
      delay: 1000,
      repeat: timeRemaining - 1,
      callback: () => {
        timeRemaining--;
        this.timeText.setText(`Time: ${timeRemaining}`);
        
        // Warning color when time is low
        if (timeRemaining <= 5) {
          this.timeText.setColor('#FF0000');
        }
      }
    });
  }

  private handleRoundComplete(nextRound: RoundData): void {
    console.log('Round complete! Next round:', nextRound);
    
    // Clear round timer
    if (this.roundTimer) {
      this.roundTimer.destroy();
      this.roundTimer = null;
    }

    // Show round complete message
    const { width, height } = this.cameras.main;
    const completeText = this.add.text(width / 2, height / 2, 'ROUND COMPLETE!', {
      fontSize: '48px',
      color: '#00FF00',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // Animate and transition to next round
    this.time.delayedCall(2000, () => {
      completeText.destroy();
      this.currentRound = nextRound;
      this.setupRoundFromServer(nextRound);
      this.startRoundTimer();
    });
  }

  private clearFaces(): void {
    this.faceSprites.forEach(sprite => sprite.destroy());
    this.faceSprites = [];
  }

  private endGame(): void {
    console.log('Game ended!');
    this.isGameActive = false;
    
    // Clear timers
    if (this.roundTimer) {
      this.roundTimer.destroy();
      this.roundTimer = null;
    }

    // Transition to end scene
    this.scene.start('GameEndScene');
  }

  private showError(message: string): void {
    console.error('Game error:', message);
    
    // Show error to user (you might want to integrate with your global error system)
    if (typeof window !== 'undefined' && (window as any).showGameError) {
      (window as any).showGameError(message, 'error');
    }
  }
}
