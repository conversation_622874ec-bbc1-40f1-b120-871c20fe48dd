import * as Phaser from 'phaser';
import type { SocketClient } from '$lib/socket';
import type {
  FindingLuigiConfig,
  RoundData,
  FaceData,
  FaceSprite,
  TargetDisplay,
  ActionResultData,
  GameStartedData
} from '../types/types';
import { GAME_CONFIG, COLORS } from '../config/GameConfig';

export default class GameScene extends Phaser.Scene {
  private socketClient: SocketClient | null = null;
  
  // Game state
  private currentRound: RoundData | null = null;
  private faceSprites: FaceSprite[] = [];
  private targetDisplays: TargetDisplay[] = [];
  private isGameActive: boolean = false;
  private isWaitingForServer: boolean = false;
  
  // UI elements (using generic components)
  private instructionText!: Phaser.GameObjects.Text;
  
  // Timers
  private roundTimer: Phaser.Time.TimerEvent | null = null;
  
  constructor() {
    super('GameScene');
  }

  init(): void {
    // Reset game state
    this.isGameActive = false;
    this.isWaitingForServer = false;
    this.currentRound = null;
    this.faceSprites = [];
    this.targetDisplays = [];

    // Get socket client from game registry
    const gameConfig = this.registry.get('gameConfig') as FindingLuigiConfig;
    this.socketClient = gameConfig?.socketClient || null;

    // Setup socket event listeners
    this.setupSocketEventListeners();
  }

  /**
   * Setup socket event listeners for server communication
   */
  private setupSocketEventListeners(): void {
    if (!this.socketClient) return;

    // Add custom event listeners for game-specific handling
    this.socketClient.addCustomEventListener('initialized', (data: GameStartedData) => {
      console.log('Finding Luigi game initialized by server:', data);

      // Sync game state from server
      if (data.firstRound) {
        console.log('First round data received from server:', data.firstRound);
        this.currentRound = data.firstRound;
        this.setupRoundFromServer(data.firstRound);
      }

      // Start countdown after initialization
      this.startCountdown();
    });

    this.socketClient.addCustomEventListener('started', (data: any) => {
      console.log('Finding Luigi game started by server:', data);
      this.startGame();
    });

    this.socketClient.addCustomEventListener('action_result', (data: ActionResultData) => {
      console.log('Action result from server:', data);
      if (data.actionType === 'face_select') {
        this.handleFaceSelectResult(data.data);
      }
    });

    this.socketClient.addCustomEventListener('ended', (data: any) => {
      console.log('Finding Luigi game ended by server:', data);
      this.endGame();
    });

    this.socketClient.addCustomEventListener('error', (data: any) => {
      console.error('Server error:', data);
      this.showError(data.message || 'Unknown server error');
    });
  }

  create(): void {
    // Create UI elements
    this.createUI();

    // Initialize game first (client load)
    this.time.delayedCall(100, () => {
      this.initializeGame();
    });
  }

  private createUI(): void {
    const { width, height } = this.cameras.main;

    // Simple background
    this.add.rectangle(width / 2, height / 2, width, height, 0x87CEEB);

    // Instruction text
    this.instructionText = this.add.text(width / 2, 50, 'Find the Blue Hat!', {
      fontSize: '32px',
      color: '#FFFFFF',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);
  }

  private initializeGame(): void {
    if (!this.socketClient) {
      console.error('No socket client available');
      this.showError('Connection error');
      return;
    }

    console.log('Initializing Finding Luigi game...');
    this.socketClient.initGame();
  }

  private async startCountdown(): Promise<void> {
    console.log('Starting countdown...');
    
    // const { width, height } = this.cameras.main;
    
    // // Create countdown overlay
    // const overlay = this.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.7);
    // const countdownText = this.add.text(width / 2, height / 2, '3', {
    //   fontSize: '120px',
    //   color: '#FFFFFF',
    //   fontFamily: 'Arial, sans-serif',
    //   fontStyle: 'bold'
    // }).setOrigin(0.5);

    // let count = 3;
    // this.time.addEvent({
    //   delay: 1000,
    //   repeat: 2,
    //   callback: () => {
    //     count--;
    //     if (count > 0) {
    //       countdownText.setText(count.toString());
    //     } else {
    //       countdownText.setText('GO!');
    //       this.time.delayedCall(500, () => {
    //         overlay.destroy();
    //         countdownText.destroy();
    //         this.startGameFromCountdown();
    //       });
    //     }
    //   }
    // });

    for (let i = 0; i < 4; i++) {
      // Play sound
      try {
        this.sound.play(i === 3 ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      await new Promise<void>((resolve) => {
        this.time.delayedCall(1300, () => resolve());
      });
    }

    // Send game start event to server using proper format
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.startGame();
    }
  }

  private startGameFromCountdown(): void {

  }

  private startGame(): void {
    console.log('Game started!');
    this.isGameActive = true;

    // Start round timer if we have round data
    if (this.currentRound) {
      this.startRoundTimer();
    }
  }

  update(_time: number, delta: number): void {
    if (!this.isGameActive) return;

    // Update face movement
    this.updateFaceMovement(delta);
  }

  private updateFaceMovement(delta: number): void {
    const currentTime = Date.now();

    this.faceSprites.forEach(sprite => {
      if (sprite.isFound) return; // Don't move found faces

      // Change direction periodically
      if (currentTime - sprite.lastDirectionChange > GAME_CONFIG.MOVEMENT.DIRECTION_CHANGE_INTERVAL) {
        sprite.velocity = {
          x: (Math.random() - 0.5) * sprite.speed,
          y: (Math.random() - 0.5) * sprite.speed
        };
        sprite.lastDirectionChange = currentTime;
      }

      // Update position
      sprite.x += sprite.velocity.x * (delta / 1000);
      sprite.y += sprite.velocity.y * (delta / 1000);

      // Bounce off screen edges
      const { width, height } = this.cameras.main;
      const padding = 50;

      if (sprite.x < padding || sprite.x > width - padding) {
        sprite.velocity.x *= -1;
        sprite.x = Phaser.Math.Clamp(sprite.x, padding, width - padding);
      }

      if (sprite.y < padding + 100 || sprite.y > height - padding) { // Extra padding at top for UI
        sprite.velocity.y *= -1;
        sprite.y = Phaser.Math.Clamp(sprite.y, padding + 100, height - padding);
      }
    });
  }

  private setupRoundFromServer(roundData: RoundData): void {
    console.log('Setting up round from server:', roundData);

    // Clear existing faces
    this.clearFaces();

    // Update instruction text
    this.instructionText.setText(`Round ${roundData.roundNumber} - Find the Blue Hat!`);

    // Create face sprites from layout
    this.createFaceSprites(roundData.layout.faces);

    // Mark target faces
    this.markTargetFaces(roundData.targets);
  }

  private markTargetFaces(targets: string[]): void {
    // Mark face sprites that are targets
    this.faceSprites.forEach(sprite => {
      sprite.isTarget = targets.includes(sprite.faceData.faceType);
    });
  }

  private createFaceSprites(faces: FaceData[]): void {
    faces.forEach(faceData => {
      // Create face sprite
      const sprite = this.add.image(
        faceData.position.x,
        faceData.position.y,
        faceData.faceType
      ) as FaceSprite;

      // Apply transformations
      sprite.setScale(faceData.position.scale);
      sprite.setRotation(faceData.position.rotation * Math.PI / 180);
      sprite.setDepth(faceData.zIndex);

      // Store face data
      sprite.faceData = faceData;
      sprite.isTarget = false; // Will be set when we know targets
      sprite.isFound = false;
      sprite.originalScale = faceData.position.scale;
      sprite.originalRotation = faceData.position.rotation * Math.PI / 180;

      // Initialize movement properties
      sprite.speed = Phaser.Math.Between(GAME_CONFIG.MOVEMENT.SPEED_MIN, GAME_CONFIG.MOVEMENT.SPEED_MAX);
      sprite.velocity = {
        x: (Math.random() - 0.5) * sprite.speed,
        y: (Math.random() - 0.5) * sprite.speed
      };
      sprite.lastDirectionChange = Date.now();

      // Make interactive
      sprite.setInteractive({ useHandCursor: true });
      
      sprite.on('pointerdown', () => {
        this.handleFaceClick(sprite);
      });

      sprite.on('pointerover', () => {
        if (!sprite.isFound) {
          sprite.setScale(sprite.originalScale * GAME_CONFIG.FACE_HOVER_SCALE);
        }
      });

      sprite.on('pointerout', () => {
        if (!sprite.isFound) {
          sprite.setScale(sprite.originalScale);
        }
      });

      this.faceSprites.push(sprite);
    });
  }



  private handleFaceClick(sprite: FaceSprite): void {
    if (!this.isGameActive || this.isWaitingForServer || sprite.isFound) {
      return;
    }

    console.log('Face clicked:', sprite.faceData.faceType, sprite.faceData.id);

    // Visual feedback
    sprite.setScale(sprite.originalScale * GAME_CONFIG.FACE_CLICK_SCALE);
    this.time.delayedCall(GAME_CONFIG.ANIMATION_DURATION, () => {
      if (!sprite.isFound) {
        sprite.setScale(sprite.originalScale);
      }
    });

    // Send selection to server
    this.sendFaceSelection(sprite);
  }

  private sendFaceSelection(sprite: FaceSprite): void {
    if (!this.socketClient) return;

    this.isWaitingForServer = true;

    const reactionTime = this.currentRound ? Date.now() - this.currentRound.startTime : 0;

    this.socketClient.sendFaceSelect(
      sprite.faceData.id,
      reactionTime,
      { x: sprite.x, y: sprite.y }
    );
  }

  private handleFaceSelectResult(data: any): void {
    this.isWaitingForServer = false;

    console.log('Face select result:', data);

    // Find the clicked face sprite
    const sprite = this.faceSprites.find(s => s.faceData.id === data.faceId);
    
    if (sprite) {
      if (data.wasTarget && data.isCorrect) {
        // Correct target found (blue hat)
        this.markFaceAsFound(sprite);
        this.showScorePopup(sprite.x, sprite.y, `+${data.points}`, COLORS.SUCCESS);
      } else {
        // Wrong face clicked
        this.showScorePopup(sprite.x, sprite.y, `${data.points}`, COLORS.ERROR);
      }
    }

    // Update UI
    this.updateScore(data.newScore);
    this.updateLives(data.newLives);

    // Handle round completion or game end
    if (data.gameEnded) {
      this.endGame();
    } else if (data.roundComplete && data.nextRound) {
      this.handleRoundComplete(data.nextRound);
    }
  }

  private markFaceAsFound(sprite: FaceSprite): void {
    sprite.isFound = true;
    sprite.setTint(COLORS.FOUND_HIGHLIGHT);
    sprite.setScale(sprite.originalScale * 1.2);
    
    // Add checkmark
    const checkmark = this.add.image(sprite.x, sprite.y, 'found_checkmark')
      .setDisplaySize(40, 40)
      .setDepth(sprite.depth + 1);
    
    // Animate checkmark
    checkmark.setScale(0);
    this.tweens.add({
      targets: checkmark,
      scale: 1,
      duration: 300,
      ease: 'Back.easeOut'
    });
  }



  private showScorePopup(x: number, y: number, text: string, color: number): void {
    const popup = this.add.text(x, y, text, {
      fontSize: '32px',
      color: `#${color.toString(16).padStart(6, '0')}`,
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    this.tweens.add({
      targets: popup,
      y: y + GAME_CONFIG.SCORE_POPUP_OFFSET,
      alpha: 0,
      duration: GAME_CONFIG.SCORE_POPUP_DURATION,
      ease: 'Power2',
      onComplete: () => {
        popup.destroy();
      }
    });
  }

  private updateScore(score: number): void {
    // Update score in registry for generic UI components
    this.registry.set('currentScore', score);
  }

  private updateLives(lives: number): void {
    // Update lives in registry for generic UI components
    this.registry.set('currentLives', lives);
  }

  private startRoundTimer(): void {
    if (!this.currentRound) return;

    const duration = this.currentRound.timeLimit;
    let timeRemaining = Math.floor(duration / 1000);

    this.roundTimer = this.time.addEvent({
      delay: 1000,
      repeat: timeRemaining - 1,
      callback: () => {
        timeRemaining--;
        // Update time in registry for generic UI components
        this.registry.set('timeRemaining', timeRemaining);
      }
    });
  }

  private handleRoundComplete(nextRound: RoundData): void {
    console.log('Round complete! Next round:', nextRound);
    
    // Clear round timer
    if (this.roundTimer) {
      this.roundTimer.destroy();
      this.roundTimer = null;
    }

    // Show round complete message
    const { width, height } = this.cameras.main;
    const completeText = this.add.text(width / 2, height / 2, 'ROUND COMPLETE!', {
      fontSize: '48px',
      color: '#00FF00',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // Animate and transition to next round
    this.time.delayedCall(2000, () => {
      completeText.destroy();
      this.currentRound = nextRound;
      this.setupRoundFromServer(nextRound);
      this.startRoundTimer();
    });
  }

  private clearFaces(): void {
    this.faceSprites.forEach(sprite => sprite.destroy());
    this.faceSprites = [];
  }

  private endGame(): void {
    console.log('Game ended!');
    this.isGameActive = false;
    
    // Clear timers
    if (this.roundTimer) {
      this.roundTimer.destroy();
      this.roundTimer = null;
    }

    // Transition to end scene
    this.scene.start('GameEndScene');
  }

  private showError(message: string): void {
    console.error('Game error:', message);
    
    // Show error to user (you might want to integrate with your global error system)
    if (typeof window !== 'undefined' && (window as any).showGameError) {
      (window as any).showGameError(message, 'error');
    }
  }
}
