import * as Phaser from 'phaser';

export default class GameEndScene extends Phaser.Scene {
  private finalScore: number = 0;
  private playAgainButton!: Phaser.GameObjects.Text;
  private titleText!: Phaser.GameObjects.Text;
  private scoreText!: Phaser.GameObjects.Text;
  private statsText!: Phaser.GameObjects.Text;

  constructor() {
    super('GameEndScene');
  }

  init(data: any): void {
    // Get final score from registry or passed data
    this.finalScore = data?.finalScore || this.registry.get('currentScore') || 0;
  }

  create(): void {
    const { width, height } = this.cameras.main;

    // Add background
    this.add.rectangle(width / 2, height / 2, width, height, 0x1E1E1E);

    // Game Over title
    this.titleText = this.add.text(width / 2, height * 0.15, 'GAME OVER', {
      fontSize: '48px',
      color: '#FF6B6B',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // Final score
    this.scoreText = this.add.text(width / 2, height * 0.3, `Final Score: ${this.finalScore}`, {
      fontSize: '36px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // Game statistics (placeholder for now)
    this.statsText = this.add.text(width / 2, height * 0.45, 
      'Game Statistics:\n\n' +
      '• Targets Found: --\n' +
      '• Accuracy: --%\n' +
      '• Best Reaction Time: --ms\n' +
      '• Rounds Completed: --', {
      fontSize: '20px',
      color: '#FFFFFF',
      fontFamily: 'Arial, sans-serif',
      align: 'center',
      lineSpacing: 10
    }).setOrigin(0.5);

    // Play again button
    this.playAgainButton = this.add.text(width / 2, height * 0.7, 'PLAY AGAIN', {
      fontSize: '32px',
      color: '#4CAF50',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // Make play again button interactive
    this.playAgainButton.setInteractive({ useHandCursor: true });
    
    this.playAgainButton.on('pointerdown', () => {
      this.restartGame();
    });

    this.playAgainButton.on('pointerover', () => {
      this.playAgainButton.setScale(1.1);
      this.playAgainButton.setColor('#66BB6A');
    });

    this.playAgainButton.on('pointerout', () => {
      this.playAgainButton.setScale(1.0);
      this.playAgainButton.setColor('#4CAF50');
    });

    // Add some visual effects
    this.createParticleEffects();
    
    // Animate elements in
    this.animateElementsIn();
  }

  private createParticleEffects(): void {
    const { width, height } = this.cameras.main;
    
    // Create some floating particles for visual appeal
    for (let i = 0; i < 10; i++) {
      const particle = this.add.circle(
        Phaser.Math.Between(0, width),
        Phaser.Math.Between(height, height + 100),
        Phaser.Math.Between(2, 6),
        0x4CAF50,
        0.6
      );

      // Animate particles floating up
      this.tweens.add({
        targets: particle,
        y: -50,
        duration: Phaser.Math.Between(3000, 6000),
        ease: 'Linear',
        repeat: -1,
        delay: Phaser.Math.Between(0, 2000),
        onRepeat: () => {
          particle.y = height + 50;
          particle.x = Phaser.Math.Between(0, width);
        }
      });

      // Add slight horizontal drift
      this.tweens.add({
        targets: particle,
        x: particle.x + Phaser.Math.Between(-50, 50),
        duration: Phaser.Math.Between(2000, 4000),
        yoyo: true,
        repeat: -1,
        ease: 'Sine.easeInOut'
      });
    }
  }

  private animateElementsIn(): void {
    // Start all elements off-screen or invisible
    this.titleText.setAlpha(0).setY(this.titleText.y - 50);
    this.scoreText.setAlpha(0).setScale(0);
    this.statsText.setAlpha(0).setX(this.statsText.x - 100);
    this.playAgainButton.setAlpha(0).setY(this.playAgainButton.y + 50);

    // Animate title in
    this.tweens.add({
      targets: this.titleText,
      alpha: 1,
      y: this.titleText.y + 50,
      duration: 800,
      ease: 'Back.easeOut'
    });

    // Animate score in
    this.time.delayedCall(300, () => {
      this.tweens.add({
        targets: this.scoreText,
        alpha: 1,
        scale: 1,
        duration: 600,
        ease: 'Back.easeOut'
      });
    });

    // Animate stats in
    this.time.delayedCall(600, () => {
      this.tweens.add({
        targets: this.statsText,
        alpha: 1,
        x: this.statsText.x + 100,
        duration: 600,
        ease: 'Power2.easeOut'
      });
    });

    // Animate button in
    this.time.delayedCall(900, () => {
      this.tweens.add({
        targets: this.playAgainButton,
        alpha: 1,
        y: this.playAgainButton.y - 50,
        duration: 600,
        ease: 'Back.easeOut'
      });
    });
  }

  private restartGame(): void {
    // Reset game registry
    this.registry.set('currentScore', 0);
    
    // Restart the game
    this.scene.start('GameStartScene');
  }
}
