import * as Phaser from 'phaser';
import PreloadScene from './scenes/PreloadScene';
import GameStartScene from './scenes/GameStartScene';
import GameScene from './scenes/GameScene';
import GameEndScene from './scenes/GameEndScene';
import type { SocketClient } from '$lib/socket';
import type { FindingLuigiConfig } from './types/types';

// Finding Luigi game integration
export class FindingLuigiGame {
  private config: FindingLuigiConfig;
  private gameInstance: Phaser.Game | null = null;

  constructor(config: FindingLuigiConfig) {
    this.config = config;
  }

  async init() {
    // Initialize Phaser 3 game instance
    console.log('Initializing Finding Luigi game...');

    // Game configuration
    const _config: Phaser.Types.Core.GameConfig = {
      type: Phaser.AUTO,
      width: 540,
      height: 960,
      backgroundColor: '#1E1E1E',
      parent: this.config.containerId,
      scene: [PreloadScene, GameStartScene, GameScene, GameEndScene],
      physics: {
        default: 'arcade',
        arcade: {
          gravity: { x: 0, y: 0 },
          debug: false
        }
      },
      scale: {
        mode: Phaser.Scale.EXPAND,
        autoCenter: Phaser.Scale.CENTER_BOTH,
      },
      render: {
        antialias: true,
        pixelArt: false,
        roundPixels: true,
        powerPreference: 'high-performance'
      },
      input: {
        activePointers: 3,
        windowEvents: false
      },
      dom: {
        createContainer: true
      }
    };

    this.gameInstance = new Phaser.Game(_config);

    // Pass game config (including socket client) to all scenes
    if (this.gameInstance) {
      this.gameInstance.registry.set('gameConfig', this.config);
    }
  }

  start() {
    console.log('Starting Finding Luigi game...');
    this.gameInstance?.scene.start('GameScene');
  }

  pause() {
    console.log('Pausing Finding Luigi game...');
    if (this.gameInstance) {
      this.gameInstance.scene.pause('GameScene');
    }
  }

  resume() {
    console.log('Resuming Finding Luigi game...');
    if (this.gameInstance) {
      this.gameInstance.scene.resume('GameScene');
    }
  }

  destroy() {
    console.log('Destroying Finding Luigi game...');
    if (this.gameInstance) {
      this.gameInstance.destroy(true);
      this.gameInstance = null;
    }
  }

  getCurrentScore(): number {
    // Get score from game registry or return 0
    if (this.gameInstance) {
      return this.gameInstance.registry.get('currentScore') || 0;
    }
    return 0;
  }
}
